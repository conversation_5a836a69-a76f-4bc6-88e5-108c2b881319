import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Inventory } from '../../../api/game/inventory'
import { Walking } from '../../../api/game/walking'
import { WorldHopping } from '../../../api/game/worldHopping'
import { ItemId } from '../../../data/itemId'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { GeAction } from '../../../api/game/geAction'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { GiveToMuleState } from '../../../api/script-utils/mule/giveMuleStrategy'
import { Item } from '../../../api/model/item'
import { TradePackage } from '../../../api/model/tradePackage'
import { BotSettings } from '../../../botSettings'
import { MuleReceiver } from '../../muling/muleReceiver'
import { addEquipmentManager } from '../../../api/script-utils/states/equipmentStates'
import { GetRingOfTheElementsState } from '../../mud-crafter/states/mudCrafterGe'
import { Teleport } from '../../../data/teleport'
import { Equipment } from '../../../api/game/equipment'
import { Varps } from '../../../api/game/varps'
import { ItemPredicate } from '../../../data/itemPredicates'
import { Player } from '../../../api/wrappers/player'
import { Time } from '../../../api/utils/time'
import { Tile } from '../../../api/model/tile'
import { GameObjects } from '../../../api/game/gameObjects'
import { SlotManager } from '../utils/slotManager'
import { Redis } from '../../../api/utils/redis'
import { Players } from '../../../api/game/players'
import { Trade } from '../../../api/game/trade'
import { Client } from '../../../api/wrappers/client'
import { log } from '../../../api/utils/utils'

export type ItemsRunnerSpot = 'pure-essence'


export class MainItemsRunner extends State {

    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 308)
    giveToMule = new GiveToMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_GIVE), BotSettings.tradeWorldP2p, [new Item(995, 700_000)], [new Item(995, ***********)])

     slotManager = new SlotManager("mud-runes")

    onGameMessage(username: string, message: string): void {
        if (message.includes('Accepted trade.')) {
            // Trade completed successfully, go back to banking
            this.setState(this.bankingState)
        }
    }

    onDraw(canvas: any, paint: any): void {
        this.drawText(`Slot Host: ${this.slotManager.getCurrentSlotHost() + ' ' + JSON.stringify(this.slotManager.getCurrentSlotInfo())}`)
        this.drawText(`Has Slot: ${this.slotManager.hasSlot() ? 'Yes' : 'No'}`)
        this.drawText(`Slot World: ${this.slotManager.getCurrentSlotWorld() || 'None'}`)
        this.drawText(`Current World: ${Client.currentWorld}`)

        try {
            const task = Redis.getJson("delivery:tasks:mud-runes:123456")
            this.drawText(`Task: ` + JSON.stringify(task))
        } catch (e) {
            this.drawText(`Task: Not found`)
        }
    }

    get spot() {
        return "pure-essence"
    }

    onFirstExecute(): void {
        addEquipmentManager(this, [Equipment.SLOT_HELM])

    }

    onBackgroundAction(): void {
        Walking.setRunAuto()
        this.slotManager.manageSlots()
    }

    onAction(): void {
        if (!WorldHopping.switchToP2pExcept()) {
            return
        }

        this.setState(this.bankingState)
    }

    bankingState = createState('Banking', () => {
        Walking.setRunAuto()

        if (Player.getRunEnergy() < 20) {
            this.setState(this.drinkStamina)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        // Deposit all except what we need
        Bank.depositAllExceptPredicate((i) =>
            [ItemId.RING_OF_THE_ELEMENTS_26818, ItemId.PURE_ESSENCE].includes(i.id) ||
            Teleport.ringOfDuelingPredicate(i)
        )

        // Equip earth tiara (needed to enter mud altar)
        if (!Equipment.withdrawAndEquip(ItemId.EARTH_TIARA, () => this.geState)) {
            return
        }

        // Equip ring of dueling
        if (!Equipment.withdrawAndEquipByPredicate(Teleport.ringOfDuelingPredicate, () => this.geState)) {
            return
        }

        // Withdraw ring of elements
        if (!Withdraw.id(ItemId.RING_OF_THE_ELEMENTS_26818, 1).minimumAmount(1).orState(() => new GetRingOfTheElementsState(this, this.resupply)).ensureSpace().withdraw()) {
            return
        }

        // Withdraw pure essence
        if (!Withdraw.id(ItemId.PURE_ESSENCE, 25).minimumAmount(25).orState(() => this.geState).ensureSpace().withdraw()) {
            return
        }

        this.setState(this.deliveryState)
    })

    drinkStamina = createState('Drinking stamina potion', () => {
        if (Player.getRunEnergy() > 80 && Player.isStaminaPotionActive()) {
            this.setState(this.bankingState)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        if (!Withdraw.predicate(ItemPredicate.staminaPotion, 1).orState(() => this.geState).withdraw()) {
            return
        }

        if (Bank.isOpen()) {
            Inventory.get(Inventory.bank).getByPredicate(ItemPredicate.staminaPotion)?.click(1007, 9)
        } else {
            Inventory.getByPredicate(ItemPredicate.staminaPotion)?.click(57, 2)
        }

        Time.sleep(400, 1000)
    })

    deliveryState = createState('Delivering pure essence', () => {
        // If no pure essence left, go back to banking
        if (!Trade.isOpen!Inventory.contains(ItemId.PURE_ESSENCE)) {
            this.setState(this.bankingState)
            return
        }

        // Check if we need to hop to the correct world
        const slotWorld = this.slotManager.getCurrentSlotWorld()
        if (slotWorld && Client.currentWorld !== slotWorld) {
            log(`[Delivery] Hopping to slot world: ${slotWorld}`)
            if (!WorldHopping.switchToWorld(slotWorld)) {
                return
            }
        }

        // If we're in a trade, handle the trade
        if (Trade.isOpen()) {
            // Add all pure essence to trade
            const pureEssenceCount = Inventory.get().getCount(ItemId.PURE_ESSENCE)
            if (pureEssenceCount > 0) {
                Trade.add(ItemId.PURE_ESSENCE, pureEssenceCount)
            }

            // Accept the trade
            Trade.accept()
            return
        }

        // Check if we're inside the earth altar
        if (new Tile(2658, 4839, 0).distance() < 20) {
            // We're inside the altar, look for the slot host player
            const slotHost = this.slotManager.getCurrentSlotHost()
            if (!slotHost) {
                // No slot host, wait or go back to banking
                Time.sleep(2000)
                return
            }

            // Look for the player
            const hostPlayer = Players.byName(slotHost)
            if (!hostPlayer) {
                // Player not found, wait
                Time.sleep(1000)
                return
            }

            // Check if player is close enough
            if (hostPlayer.tile.distance() > 3) {
                // Walk closer to the player
                if (!Walking.walkTo(hostPlayer.tile, 1)) return
                return
            }

            // Click on the player to initiate trade
            hostPlayer.click(2047) // Trade option
            Time.sleep(4000, 7500)
            return
        }

        // Check if we're near the earth altar entrance
        if (new Tile(3308, 3474, 0).distance() > 30) {
            // Use ring of elements to teleport to earth altar
            Teleport.ringOfTheElementsEarth.process()
            return
        } else {
            // Walk to the earth altar entrance and enter it
            const obj = GameObjects.getNearest(new Tile(3308, 3474, 0), (o) => o.realId == 29099, 10)
            if (obj == null && !Walking.walkTo(new Tile(3308, 3474, 0), 6)) return

            obj?.click(3)
            Time.sleep(() => new Tile(2658, 4839, 0).distance() < 30)
        }
    })

    geState = createGeState(
        () => this,
        () => this.resupply,
        [
            GeAction.item(ItemId.PURE_ESSENCE, 3000).gePrice(1.12, 5).buy(),
            GeAction.item(ItemId.RING_OF_DUELING8, 40).gePrice(1.12, 1000).buy(),
            GeAction.item(ItemId.STAMINA_POTION4, 15).gePrice(1.1, 1000).buy(),
            GeAction.item(ItemId.EARTH_TIARA, 1).gePrice(1.12, 1555).withEq().buy(),
        ], 'Items Runner GE'
    )

 
}
